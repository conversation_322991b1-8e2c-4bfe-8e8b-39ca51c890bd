import React from 'react';
import { useChatStore } from '../../store/useChatStore';
import ChatHeader from './ChatHeader';
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import TypingIndicator from './TypingIndicator';

const ChatWindow: React.FC = () => {
  const { currentChat } = useChatStore();

  if (!currentChat) {
    return null;
  }

  return (
    <div className="bg-gray-800 rounded-lg flex flex-col overflow-hidden">
      {/* Chat Header */}
      <ChatHeader chat={currentChat} />

      {/* Messages Area */}
      <div className="flex-1 flex flex-col min-h-0 overflow-hidden">
        <MessageList chatId={currentChat.id} />
        <TypingIndicator chatId={currentChat.id} />
      </div>

      {/* Message Input */}
      <MessageInput chatId={currentChat.id} />
    </div>
  );
};

export default ChatWindow;
