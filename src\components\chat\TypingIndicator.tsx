import React from 'react';
import { useChatStore } from '../../store/useChatStore';
import useAuthStore from '../../store/useAuthStore';

interface TypingIndicatorProps {
  chatId: string;
}

const TypingIndicator: React.FC<TypingIndicatorProps> = ({ chatId }) => {
  const { user } = useAuthStore();
  const { typingUsers } = useChatStore();

  const chatTypingUsers = typingUsers[chatId] || [];
  const otherUsersTyping = chatTypingUsers.filter(typing => typing.userId !== user?.id);

  if (otherUsersTyping.length === 0) {
    return null;
  }

  const getTypingText = (): string => {
    const usernames = otherUsersTyping.map(typing => typing.username);
    
    if (usernames.length === 1) {
      return `${usernames[0]} is typing...`;
    } else if (usernames.length === 2) {
      return `${usernames[0]} and ${usernames[1]} are typing...`;
    } else {
      return `${usernames[0]} and ${usernames.length - 1} others are typing...`;
    }
  };

  return (
    <div className="px-4 py-2 text-sm text-gray-400 italic">
      <div className="flex items-center space-x-2">
        {/* Typing animation dots */}
        <div className="flex space-x-1">
          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
        </div>
        <span>{getTypingText()}</span>
      </div>
    </div>
  );
};

export default TypingIndicator;
