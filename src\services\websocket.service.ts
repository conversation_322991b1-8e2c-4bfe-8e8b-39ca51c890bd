import { io, Socket } from 'socket.io-client';
import { Message, TypingEvent, MessageEvent, WebSocketEvents } from '../types/chat';

class WebSocketService {
  private socket: Socket | null = null;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  // Event listeners
  private messageListeners: ((message: Message) => void)[] = [];
  private typingListeners: ((event: TypingEvent) => void)[] = [];
  private connectionListeners: ((connected: boolean) => void)[] = [];
  private errorListeners: ((error: any) => void)[] = [];

  connect(token: string): void {
    if (!token) {
      console.warn('Cannot connect WebSocket: No token provided');
      return;
    }

    if (this.socket?.connected) {
      return;
    }

    try {
      const API_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000';

      this.socket = io(API_URL, {
        auth: {
          token: token
        },
        transports: ['websocket', 'polling'],
        timeout: 20000,
        forceNew: true
      });

      this.setupEventListeners();
    } catch (error) {
      console.error('Failed to initialize WebSocket connection:', error);
      this.notifyErrorListeners(error);
    }
  }

  private setupEventListeners(): void {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('WebSocket connected');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.notifyConnectionListeners(true);
    });

    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason);
      this.isConnected = false;
      this.notifyConnectionListeners(false);

      if (reason === 'io server disconnect') {
        return;
      }

      this.attemptReconnect();
    });

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      this.notifyErrorListeners(error);
      this.attemptReconnect();
    });

    // Fixed: Backend-specific events
    this.socket.on('connected', (data) => {
      console.log('Successfully connected to chat system:', data);
    });

    this.socket.on('new_message', (data: MessageEvent) => {
      console.log('New message received:', data);
      if(data.message){
        this.notifyMessageListeners(data.message);
      }
    });

    this.socket.on('user_typing', (event: TypingEvent) => {
      this.notifyTypingListeners(event);
    });

    this.socket.on('messages_read', (data) => {
      console.log('Messages marked as read:', data);
      // You can add a callback for this if needed
    });

    this.socket.on('joined_chat', (data) => {
      console.log('Joined chat:', data.chatId);
    });

    this.socket.on('left_chat', (data) => {
      console.log('Left chat:', data.chatId);
    });

    this.socket.on('error', (error: any) => {
      console.error('WebSocket error:', error);
      this.notifyErrorListeners(error);
    });
  }

  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);

    setTimeout(() => {
      if (this.socket && !this.socket.connected) {
        this.socket.connect();
      }
    }, delay);
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      this.notifyConnectionListeners(false);
    }
  }

  // Chat room management
  joinChat(chatId: string): void {
    if (this.socket?.connected) {
      this.socket.emit('join_chat', { chatId });
    }
  }

  leaveChat(chatId: string): void {
    if (this.socket?.connected) {
      this.socket.emit('leave_chat', { chatId });
    }
  }

  // Typing indicators
  startTyping(chatId: string): void {
    if (this.socket?.connected) {
      this.socket.emit('typing_start', { chatId });
    }
  }

  stopTyping(chatId: string): void {
    if (this.socket?.connected) {
      this.socket.emit('typing_stop', { chatId });
    }
  }

  // Event listener management
  onMessage(callback: (message: Message) => void): () => void {
    this.messageListeners.push(callback);
    return () => {
      this.messageListeners = this.messageListeners.filter(cb => cb !== callback);
    };
  }

  onTyping(callback: (event: TypingEvent) => void): () => void {
    this.typingListeners.push(callback);
    return () => {
      this.typingListeners = this.typingListeners.filter(cb => cb !== callback);
    };
  }

  onConnection(callback: (connected: boolean) => void): () => void {
    this.connectionListeners.push(callback);
    return () => {
      this.connectionListeners = this.connectionListeners.filter(cb => cb !== callback);
    };
  }

  onError(callback: (error: any) => void): () => void {
    this.errorListeners.push(callback);
    return () => {
      this.errorListeners = this.errorListeners.filter(cb => cb !== callback);
    };
  }

  // Notification methods
  private notifyMessageListeners(message: Message): void {
    this.messageListeners.forEach(callback => callback(message));
  }

  private notifyTypingListeners(event: TypingEvent): void {
    this.typingListeners.forEach(callback => callback(event));
  }

  private notifyConnectionListeners(connected: boolean): void {
    this.connectionListeners.forEach(callback => callback(connected));
  }

  private notifyErrorListeners(error: any): void {
    this.errorListeners.forEach(callback => callback(error));
  }

  // Getters
  get connected(): boolean {
    return this.isConnected && this.socket?.connected === true;
  }
}

// Export singleton instance
export const websocketService = new WebSocketService();
export default websocketService;
