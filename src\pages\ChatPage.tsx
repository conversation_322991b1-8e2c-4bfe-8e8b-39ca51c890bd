import React, { useEffect, useState } from 'react';
import { useAuthGuard } from '../hooks/useAuthGuard';
import { useWebSocket } from '../hooks/useWebSocket';
import { useChatStore } from '../store/useChatStore';
import Navbar from '../components/Navbar';
import ChatList from '../components/chat/ChatList';
import ChatWindow from '../components/chat/ChatWindow';
import CreateChatModal from '../components/chat/CreateChatModal';
import ErrorBoundary from '../components/ErrorBoundary';
import { MessageCircle, Plus, Wifi, WifiOff } from 'lucide-react';

const ChatPage: React.FC = () => {
  useAuthGuard();
  const { connected } = useWebSocket();
  const { currentChat, loadChats, loading } = useChatStore();
  const [showCreateModal, setShowCreateModal] = useState(false);

  useEffect(() => {
    // Load chats when component mounts
    loadChats();
  }, [loadChats]);

  return (
    <div className="min-h-screen bg-gray-900">
      <Navbar />

      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <MessageCircle className="w-8 h-8 text-neonBlue mr-3" />
            <h1 className="text-3xl font-bold text-white">Messages</h1>
            <div className="ml-4 flex items-center">
              {connected ? (
                <div className="flex items-center text-green-400">
                  <Wifi className="w-4 h-4 mr-1" />
                  <span className="text-sm">Connected</span>
                </div>
              ) : (
                <div className="flex items-center text-red-400">
                  <WifiOff className="w-4 h-4 mr-1" />
                  <span className="text-sm">Disconnected</span>
                </div>
              )}
            </div>
          </div>

          <button
            onClick={() => setShowCreateModal(true)}
            className="bg-neonBlue hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <Plus className="w-4 h-4 mr-2" />
            New Chat
          </button>
        </div>

        {/* Main Chat Interface */}
        <ErrorBoundary>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-180px)] min-h-[500px]">
            {/* Chat List */}
            <div className="lg:col-span-1 h-full">
              <ErrorBoundary>
                <ChatList />
              </ErrorBoundary>
            </div>

            {/* Chat Window */}
            <div className="lg:col-span-2 h-full">
              <ErrorBoundary>
                {currentChat ? (
                  <ChatWindow />
                ) : (
                  <div className="bg-gray-800 rounded-lg h-full flex items-center justify-center">
                    <div className="text-center">
                      <MessageCircle className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                      <h3 className="text-xl font-semibold text-gray-400 mb-2">
                        Select a chat to start messaging
                      </h3>
                      <p className="text-gray-500">
                        Choose a conversation from the list or start a new one
                      </p>
                    </div>
                  </div>
                )}
              </ErrorBoundary>
            </div>
          </div>
        </ErrorBoundary>
      </div>

      {/* Create Chat Modal */}
      {showCreateModal && (
        <CreateChatModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
        />
      )}
    </div>
  );
};

export default ChatPage;
