import React, { useEffect } from 'react';
import { useWebSocket } from '../hooks/useWebSocket';
import useAuthStore from '../store/useAuthStore';

interface WebSocketProviderProps {
  children: React.ReactNode;
}

const WebSocketProvider: React.FC<WebSocketProviderProps> = ({ children }) => {
  const { access_token } = useAuthStore();
  
  // Initialize WebSocket connection when component mounts
  useWebSocket();

  return <>{children}</>;
};

export default WebSocketProvider;
