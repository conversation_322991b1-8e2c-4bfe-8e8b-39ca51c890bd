import React, { useEffect, useRef, useState } from 'react';
import { useChatStore } from '../../store/useChatStore';
import { Message } from '../../types/chat';
import useAuthStore from '../../store/useAuthStore';
import { format, isToday, isYesterday, isSameDay } from 'date-fns';
import { ChevronDown, Check, CheckCheck } from 'lucide-react';

interface MessageListProps {
  chatId: string;
}

const MessageList: React.FC<MessageListProps> = ({ chatId }) => {
  const { user } = useAuthStore();
  const {
    messages,
    messagesHasMore,
    loadMessages,
    loading
  } = useChatStore();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [autoScroll, setAutoScroll] = useState(true);

  const chatMessages = messages[chatId] || [];

  // Scroll to bottom when new messages arrive (if user is near bottom)
  useEffect(() => {
    if (autoScroll && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [chatMessages, autoScroll]);

  // Handle scroll events
  const handleScroll = () => {
    if (!messagesContainerRef.current) return;

    const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;
    const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;

    setAutoScroll(isNearBottom);
    setShowScrollButton(!isNearBottom);

    // Load more messages when scrolled to top
    if (scrollTop === 0 && messagesHasMore[chatId] && !loading) {
      loadMessages(chatId, true);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    setAutoScroll(true);
    setShowScrollButton(false);
  };

  const formatMessageTime = (dateString: string): string => {
    try {
      if (!dateString) return '';

      const date = new Date(dateString);

      // Check if date is valid
      if (isNaN(date.getTime())) {
        return '';
      }

      if (isToday(date)) {
        return format(date, 'HH:mm');
      } else if (isYesterday(date)) {
        return `Yesterday ${format(date, 'HH:mm')}`;
      } else {
        return format(date, 'MMM dd, HH:mm');
      }
    } catch (error) {
      console.warn('Error formatting message time:', error);
      return '';
    }
  };

  const shouldShowDateSeparator = (currentMessage: Message, previousMessage?: Message): boolean => {
    if (!previousMessage) return true;

    const currentDate = new Date(currentMessage.createdAt);
    const previousDate = new Date(previousMessage.createdAt);

    return !isSameDay(currentDate, previousDate);
  };

  const formatDateSeparator = (dateString: string): string => {
    const date = new Date(dateString);

    if (isToday(date)) {
      return 'Today';
    } else if (isYesterday(date)) {
      return 'Yesterday';
    } else {
      return format(date, 'MMMM dd, yyyy');
    }
  };

  const isConsecutiveMessage = (currentMessage: Message, previousMessage?: Message): boolean => {
    if (!previousMessage) return false;

    const isSameSender = currentMessage.sender?.id === previousMessage.sender?.id;
    const timeDiff = new Date(currentMessage.createdAt).getTime() - new Date(previousMessage.createdAt).getTime();
    const isWithinTimeLimit = timeDiff < 5 * 60 * 1000; // 5 minutes

    return isSameSender && isWithinTimeLimit;
  };

  if (chatMessages.length === 0 && !loading) {
    return (
      <div className="flex-1 flex items-center justify-center text-gray-400">
        <div className="text-center">
          <p className="text-lg mb-2">No messages yet</p>
          <p className="text-sm">Start the conversation!</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 relative overflow-hidden">
      {/* Messages Container */}
      <div
        ref={messagesContainerRef}
        onScroll={handleScroll}
        className="h-full overflow-y-auto px-4 py-2 space-y-1 chat-scrollbar"
      >
        {/* Load More Indicator */}
        {messagesHasMore[chatId] && (
          <div className="text-center py-2">
            {loading ? (
              <span className="text-gray-400 text-sm">Loading more messages...</span>
            ) : (
              <span className="text-gray-500 text-sm">Scroll up to load more</span>
            )}
          </div>
        )}

        {/* Messages */}
        {chatMessages.map((message, index) => {
          // Safety check for message data
          if (!message || !message.id) {
            return null;
          }

          const previousMessage = index > 0 ? chatMessages[index - 1] : undefined;
          const isOwnMessage = message.sender?.id === user?.id;
          const showDateSeparator = shouldShowDateSeparator(message, previousMessage);
          const isConsecutive = isConsecutiveMessage(message, previousMessage);

          return (
            <div key={message.id}>
              {/* Date Separator */}
              {showDateSeparator && message.createdAt && (
                <div className="flex items-center justify-center my-4">
                  <div className="bg-gray-700 text-gray-300 text-xs px-3 py-1 rounded-full">
                    {formatDateSeparator(message.createdAt)}
                  </div>
                </div>
              )}

              {/* Message */}
              <div className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'} ${isConsecutive ? 'mt-1' : 'mt-4'}`}>
                <div className={`max-w-xs lg:max-w-md ${isOwnMessage ? 'order-2' : 'order-1'}`}>
                  {/* Sender Name (only for non-consecutive messages from others) */}
                  {!isOwnMessage && !isConsecutive && message.sender && (
                    <div className="text-xs text-gray-400 mb-1 px-3">
                      {message.sender.username || 'Unknown'}
                    </div>
                  )}

                  {/* Message Bubble */}
                  <div
                    className={`px-4 py-2 rounded-lg ${
                      isOwnMessage
                        ? 'bg-neonBlue text-white'
                        : 'bg-gray-700 text-white'
                    } ${
                      isConsecutive
                        ? isOwnMessage
                          ? 'rounded-tr-sm'
                          : 'rounded-tl-sm'
                        : ''
                    }`}
                  >
                    <p className="text-sm whitespace-pre-wrap break-words">
                      {message.content || ''}
                    </p>

                    {/* Message Time and Status */}
                    {message.createdAt && (
                      <div className={`text-xs mt-1 flex items-center gap-1 ${isOwnMessage ? 'text-blue-100 justify-end' : 'text-gray-400'}`}>
                        <span>{formatMessageTime(message.createdAt)}</span>
                        {/* Status indicator for own messages */}
                        {isOwnMessage && (
                          <span className="flex-shrink-0">
                            {message.status === 'sent' && <Check className="w-3 h-3" />}
                            {message.status === 'delivered' && <CheckCheck className="w-3 h-3" />}
                            {message.status === 'read' && <CheckCheck className="w-3 h-3 text-blue-300" />}
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          );
        }).filter(Boolean)}

        {/* Scroll anchor */}
        <div ref={messagesEndRef} />
      </div>

      {/* Scroll to Bottom Button */}
      {showScrollButton && (
        <button
          onClick={scrollToBottom}
          className="absolute bottom-4 right-4 bg-neonBlue hover:bg-blue-600 text-white p-2 rounded-full shadow-lg transition-colors"
        >
          <ChevronDown className="w-5 h-5" />
        </button>
      )}
    </div>
  );
};

export default MessageList;
