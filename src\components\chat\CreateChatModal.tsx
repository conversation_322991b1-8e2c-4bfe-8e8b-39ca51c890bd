import React, { useState, useEffect } from 'react';
import { useChatStore } from '../../store/useChatStore';
import { chatService } from '../../services/api/chat.service';
import { X, Search, User, Users, Plus } from 'lucide-react';
import { showErrorToast } from '../../utils/showErrorToast';
import { showSuccessToast } from '../../utils/showSuccessToast';

interface CreateChatModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface SearchUser {
  id: number;
  username: string;
  avatarUrl?: string;
}

const CreateChatModal: React.FC<CreateChatModalProps> = ({ isOpen, onClose }) => {
  const { createChat } = useChatStore();
  const [chatType, setChatType] = useState<'direct' | 'group'>('direct');
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchUser[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<SearchUser[]>([]);
  const [groupName, setGroupName] = useState('');
  const [groupDescription, setGroupDescription] = useState('');
  const [loading, setLoading] = useState(false);
  const [searching, setSearching] = useState(false);

  // Search for users
  useEffect(() => {
    const searchUsers = async () => {
      if (searchQuery.trim().length < 2) {
        setSearchResults([]);
        return;
      }

      setSearching(true);
      try {
        const results = await chatService.searchUsers(searchQuery);
        setSearchResults(results);
      } catch (error) {
        console.error('Failed to search users:', error);
        setSearchResults([]);
      } finally {
        setSearching(false);
      }
    };

    const debounceTimer = setTimeout(searchUsers, 300);
    return () => clearTimeout(debounceTimer);
  }, [searchQuery]);

  const handleUserSelect = (user: SearchUser) => {
    if (selectedUsers.find(u => u.id === user.id)) {
      setSelectedUsers(selectedUsers.filter(u => u.id !== user.id));
    } else {
      if (chatType === 'direct' && selectedUsers.length >= 1) {
        setSelectedUsers([user]); // Replace for direct chat
      } else {
        setSelectedUsers([...selectedUsers, user]);
      }
    }
  };

  const handleCreateChat = async () => {
    if (selectedUsers.length === 0) {
      showErrorToast('Please select at least one user');
      return;
    }

    if (chatType === 'group' && !groupName.trim()) {
      showErrorToast('Please enter a group name');
      return;
    }

    setLoading(true);
    try {
      const chatData = {
        type: chatType,
        participantIds: selectedUsers.map(u => u.id),
        ...(chatType === 'group' && {
          name: groupName.trim(),
          description: groupDescription.trim() || undefined
        })
      };

      const newChat = await createChat(chatData);
      if (newChat) {
        showSuccessToast(`${chatType === 'group' ? 'Group' : 'Chat'} created successfully!`);
        onClose();
        resetForm();
      }
    } catch (error) {
      console.error('Failed to create chat:', error);
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setChatType('direct');
    setSearchQuery('');
    setSearchResults([]);
    setSelectedUsers([]);
    setGroupName('');
    setGroupDescription('');
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg w-full max-w-lg mx-4 max-h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h2 className="text-xl font-semibold text-white">New Chat</h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-white p-1"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4">
          {/* Chat Type Selection */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Chat Type
            </label>
            <div className="flex space-x-4">
              <button
                onClick={() => setChatType('direct')}
                className={`flex items-center px-4 py-2 rounded-lg border transition-colors ${
                  chatType === 'direct'
                    ? 'border-neonBlue bg-neonBlue bg-opacity-20 text-neonBlue'
                    : 'border-gray-600 text-gray-400 hover:border-gray-500'
                }`}
              >
                <User className="w-4 h-4 mr-2" />
                Direct Message
              </button>
              <button
                onClick={() => setChatType('group')}
                className={`flex items-center px-4 py-2 rounded-lg border transition-colors ${
                  chatType === 'group'
                    ? 'border-neonBlue bg-neonBlue bg-opacity-20 text-neonBlue'
                    : 'border-gray-600 text-gray-400 hover:border-gray-500'
                }`}
              >
                <Users className="w-4 h-4 mr-2" />
                Group Chat
              </button>
            </div>
          </div>

          {/* Group Details (only for group chats) */}
          {chatType === 'group' && (
            <div className="mb-4 space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Group Name *
                </label>
                <input
                  type="text"
                  value={groupName}
                  onChange={(e) => setGroupName(e.target.value)}
                  placeholder="Enter group name"
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-neonBlue"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Description (optional)
                </label>
                <textarea
                  value={groupDescription}
                  onChange={(e) => setGroupDescription(e.target.value)}
                  placeholder="Enter group description"
                  rows={2}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-neonBlue resize-none"
                />
              </div>
            </div>
          )}

          {/* User Search */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {chatType === 'direct' ? 'Select User' : 'Add Participants'}
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search users by username..."
                className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-neonBlue"
              />
            </div>
          </div>

          {/* Selected Users */}
          {selectedUsers.length > 0 && (
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Selected ({selectedUsers.length})
              </label>
              <div className="flex flex-wrap gap-2">
                {selectedUsers.map((user) => (
                  <div
                    key={user.id}
                    className="flex items-center bg-neonBlue bg-opacity-20 text-neonBlue px-3 py-1 rounded-full text-sm"
                  >
                    <span>{user.username}</span>
                    <button
                      onClick={() => handleUserSelect(user)}
                      className="ml-2 text-neonBlue hover:text-blue-400"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Search Results */}
          {searchQuery.length >= 2 && (
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Search Results
              </label>
              <div className="max-h-48 overflow-y-auto border border-gray-600 rounded-lg">
                {searching ? (
                  <div className="p-4 text-center text-gray-400">Searching...</div>
                ) : searchResults.length === 0 ? (
                  <div className="p-4 text-center text-gray-400">No users found</div>
                ) : (
                  searchResults.map((user) => {
                    const isSelected = selectedUsers.find(u => u.id === user.id);
                    return (
                      <button
                        key={user.id}
                        onClick={() => handleUserSelect(user)}
                        className={`w-full p-3 text-left hover:bg-gray-700 transition-colors flex items-center justify-between ${
                          isSelected ? 'bg-gray-700' : ''
                        }`}
                      >
                        <div className="flex items-center">
                          <div className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center text-white text-sm font-semibold mr-3">
                            {user.username.charAt(0).toUpperCase()}
                          </div>
                          <span className="text-white">{user.username}</span>
                        </div>
                        {isSelected && (
                          <div className="text-neonBlue">
                            <Plus className="w-4 h-4 transform rotate-45" />
                          </div>
                        )}
                      </button>
                    );
                  })
                )}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-700 flex justify-end space-x-3">
          <button
            onClick={handleClose}
            className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleCreateChat}
            disabled={loading || selectedUsers.length === 0 || (chatType === 'group' && !groupName.trim())}
            className="px-4 py-2 bg-neonBlue hover:bg-blue-600 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {loading ? 'Creating...' : 'Create Chat'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CreateChatModal;
